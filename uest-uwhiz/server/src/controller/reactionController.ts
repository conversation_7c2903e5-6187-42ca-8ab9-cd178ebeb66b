import prisma from "../config/prismaClient";
import { Request, Response } from "express";

export const storeReactions = async (
  req: Request,
  res: Response
): Promise<any> => {
  const {
    studentId,
    reactorId,
    reactionType,
  }: { studentId: string; reactorId: string; reactionType: string } = req.body;

  if (!["thumbsup", "whistle", "party", "clap", "angry", "thumbsdown"].includes(reactionType)) {
    return res.status(400).json({ error: "Invalid reaction type" });
  }

  try {
    const existing = await prisma.dailyQuizReaction.findFirst({
      where: { studentId, reactorId },
    });

    let reaction;
    if (existing) {
      reaction = await prisma.dailyQuizReaction.update({
        where: { id: existing.id },
        data: { reactionType },
      });
    } else {
      // ✅ Create new reaction if none exists
      reaction = await prisma.dailyQuizReaction.create({
        data: { studentId, reactorId, reactionType, count: 1 },
      });
    }

    return res.status(200).json(reaction);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to save reaction" });
  }
};
