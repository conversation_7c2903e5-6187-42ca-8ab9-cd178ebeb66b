import { Request, Response } from 'express';
import { getMockExamCombinedData, saveMockExamResult } from '../services/mockExamResultService';


export const saveMockExamResultController = async (req: Request, res: Response): Promise<any> => {
  try {
    const data = req.body;
    const mockExamResult = await saveMockExamResult(data);
    res.status(201).json({success:true, data:mockExamResult});
  } catch (error:any) {
    res.status(400).json({ success:false ,error: error.message || error });
  }
};

export const getMockExamCombinedController = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const studentId = req.params.studentId;

    const result = await getMockExamCombinedData(studentId, page, limit);

    res.status(200).json({
      success: true,
      data: {
        mockExamResults: result.mockExamResults,
        streak: result.streak,
        badge: result.badge,
      },
      pagination: result.pagination,
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message || error,
    });
  }
};
