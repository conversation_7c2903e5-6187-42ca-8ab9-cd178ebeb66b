import { PrismaClient } from "@prisma/client";
import axios from "axios";

const prisma = new PrismaClient();

export async function getLeaderboard(
  timeframe: string,
  page: number = 1,
  limit: number = 10
): Promise<{
  data: any[];
  total: number;
  reactions: {
    [studentId: string]: {
      reaction: string | null;
      counts: {
        thumbsup: number;
        whistle: number;
        party: number;
        clap: number;
      };
    };
  };
}> {
  let startDate: Date | undefined;
  if (timeframe === "today") {
    startDate = new Date();
    startDate.setHours(0, 0, 0, 0);
  } else if (timeframe === "weekly") {
    startDate = new Date();
    startDate.setDate(startDate.getDate() - startDate.getDay());
    startDate.setHours(0, 0, 0, 0);
  }

  const skip = (page - 1) * limit;

  const total = await prisma.mockExamResult.groupBy({
    by: ['studentId'],
    where: startDate
      ? {
          createdAt: {
            gte: startDate,
          },
        }
      : undefined,
  }).then((results) => results.length);

  const groupedResults = await prisma.mockExamResult.groupBy({
    by: ["studentId"],
    _sum: {
      score: true,
    },
    where: startDate
      ? {
          createdAt: {
            gte: startDate,
          },
        }
      : undefined,
    orderBy: {
      _sum: {
        score: "desc",
      },
    },
    skip,
    take: limit,
  });

  const allStudentIds = groupedResults.map((r) => r.studentId);

  // Fetch student details
  let studentDetails: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    profile: {
      photo: string | null;
    };
  }[] = [];

  try {
    const response = await axios.post(
      `${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`,
      { applicantIds: allStudentIds },
      { headers: { "Content-Type": "application/json" } }
    );
    studentDetails = response.data;
  } catch (err) {
    console.error("Failed to fetch student details:", err);
  }

  const validStudentIds = studentDetails.map((s) => s.id);

  const filteredResults = groupedResults.filter((r) =>
    validStudentIds.includes(r.studentId)
  );

  const coinResults = await prisma.mockExamResult.groupBy({
    by: ["studentId"],
    _sum: {
      coinEarnings: true,
    },
    where: {
      studentId: {
        in: validStudentIds,
      },
    },
  });

  const streaks = await prisma.mockExamStreak.findMany({
    where: {
      studentId: {
        in: validStudentIds,
      },
    },
    select: {
      studentId: true,
      streakCount: true,
    },
  });

  const reactions = await prisma.dailyQuizReaction.findMany({
    where: {
      studentId: {
        in: validStudentIds,
      },
    },
    select: {
      studentId: true,
      reactionType: true,
    },
  });

  const reactionLogs = await prisma.dailyQuizReaction.groupBy({
    by: ["studentId", "reactionType"],
    _count: {
      reactionType: true,
    },
    where: {
      studentId: {
        in: validStudentIds,
      },
    },
  });

  const badgeImages = {
    PerfectMonth: "/Perfect Month.svg",
    PerfectYear: "/Perfect Year.svg",
    DailyStreak: "/Streak.svg",
  };

  const badgeAlts = {
    PerfectMonth: "Perfect Month Badge",
    PerfectYear: "Perfect Year Badge",
    DailyStreak: "Daily Streak Badge",
  };

  const leaderboard = filteredResults.map((result, index) => {
    const student = studentDetails.find((s) => s.id === result.studentId);
    const streak = streaks.find((s) => s.studentId === result.studentId);
    const coins = coinResults.find((c) => c.studentId === result.studentId);
    const streakCount = streak?.streakCount ?? 0;

    const badges = [];

    if (streakCount > 0) {
      badges.push({
        badgeType: "DailyStreak",
        badgeSrc: badgeImages.DailyStreak,
        badgeAlt: `${badgeAlts.DailyStreak} - ${streakCount} days`,
        count: streakCount,
      });
    }
    if (streakCount >= 30) {
      badges.push({
        badgeType: "PerfectMonth",
        badgeSrc: badgeImages.PerfectMonth,
        badgeAlt: badgeAlts.PerfectMonth,
      });
    }
    if (streakCount >= 365) {
      badges.push({
        badgeType: "PerfectYear",
        badgeSrc: badgeImages.PerfectYear,
        badgeAlt: badgeAlts.PerfectYear,
      });
    }

    return {
      rank: skip + index + 1,
      studentId: result.studentId,
      score: result._sum.score ?? 0,
      coinEarnings: coins?._sum.coinEarnings ?? 0,
      streakCount,
      firstName: student?.firstName,
      lastName: student?.lastName,
      email: student?.email,
      profilePhoto: student?.profile?.photo,
      badge: {
        streakCount,
        badges,
      },
    };
  });

  const reactionData: {
    [studentId: string]: {
      reaction: string | null;
      counts: {
        thumbsup: number;
        whistle: number;
        party: number;
        clap: number;
      };
    };
  } = {};

  validStudentIds.forEach((studentId) => {
    const reaction = reactions.find((r) => r.studentId === studentId);
    const counts = { thumbsup: 0, whistle: 0, party: 0, clap: 0 };

    reactionLogs
      .filter((log) => log.studentId === studentId)
      .forEach((log) => {
        counts[log.reactionType as keyof typeof counts] =
          log._count.reactionType;
      });

    reactionData[studentId] = {
      reaction: reaction?.reactionType || null,
      counts,
    };
  });

  return {
    data: leaderboard,
    total,
    reactions: reactionData,
  };
}
