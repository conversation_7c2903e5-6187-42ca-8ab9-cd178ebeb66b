import { Request, Response } from 'express';
import { UserType } from '@prisma/client';
import {
  getNotifications,
  getUnreadCount,
  mark<PERSON>Read,
  markAllAsRead,
  deleteAllNotifications,
  createNotification,
  createAdminNotification
} from '@/utils/notifications';
import { sendSuccess, sendError } from '@/utils/response';

export const getNotificationsController = async (req: Request, res: Response): Promise<void> => {
  try {
    let userId: string;
    let userType: UserType;

    if ((req as any).class?.id) {
      userId = (req as any).class.id;
      userType = UserType.CLASS;
    }
    else if ((req as any).student?.id) {
      userId = (req as any).student.id;
      userType = UserType.STUDENT;
    } else {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    if (page < 1 || limit < 1 || limit > 50) {
      sendError(res, 'Invalid pagination parameters. Page must be >= 1, limit must be 1-50', 400);
      return;
    }

    const result = await getNotifications(userId, userType, page, limit);
    sendSuccess(res, result, 'Notifications fetched successfully');
  } catch (error) {
    console.error('Error fetching notifications:', error);
    sendError(res, 'Failed to fetch notifications', 500);
  }
};

export const getUnreadCountController = async (req: Request, res: Response): Promise<void> => {
  try {
    let userId: string;
    let userType: UserType;

    if ((req as any).class?.id) {
      userId = (req as any).class.id;
      userType = UserType.CLASS;
    }
    else if ((req as any).student?.id) {
      userId = (req as any).student.id;
      userType = UserType.STUDENT;
    } else {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    const count = await getUnreadCount(userId, userType);
    sendSuccess(res, { count }, 'Unread count fetched successfully');
  } catch (error) {
    console.error('Error fetching unread count:', error);
    sendError(res, 'Failed to fetch unread count', 500);
  }
};

export const markAsReadController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!id) {
      sendError(res, 'Notification ID is required', 400);
      return;
    }

    const notification = await markAsRead(id);
    sendSuccess(res, notification, 'Notification marked as read');
  } catch (error) {
    console.error('Error marking notification as read:', error);
    sendError(res, 'Failed to mark notification as read', 500);
  }
};

export const markAllAsReadController = async (req: Request, res: Response): Promise<void> => {
  try {
    let userId: string;
    let userType: UserType;

    if ((req as any).class?.id) {
      userId = (req as any).class.id;
      userType = UserType.CLASS;
    }
    else if ((req as any).student?.id) {
      userId = (req as any).student.id;
      userType = UserType.STUDENT;
    } else {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    const result = await markAllAsRead(userId, userType);
    sendSuccess(res, result, 'All notifications marked as read');
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    sendError(res, 'Failed to mark all notifications as read', 500);
  }
};

export const deleteAllNotificationsController = async (req: Request, res: Response): Promise<void> => {
  try {
    let userId: string;
    let userType: UserType;

    if ((req as any).class?.id) {
      userId = (req as any).class.id;
      userType = UserType.CLASS;
    }
    else if ((req as any).student?.id) {
      userId = (req as any).student.id;
      userType = UserType.STUDENT;
    } else {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    const result = await deleteAllNotifications(userId, userType);
    sendSuccess(res, result, 'All notifications deleted successfully');
  } catch (error) {
    console.error('Error deleting all notifications:', error);
    sendError(res, 'Failed to delete all notifications', 500);
  }
};

// Admin notification controllers
export const getAdminNotificationsController = async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).admin?.id;
    if (!adminId) {
      sendError(res, 'Admin not authenticated', 401);
      return;
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    if (page < 1 || limit < 1 || limit > 50) {
      sendError(res, 'Invalid pagination parameters. Page must be >= 1, limit must be 1-50', 400);
      return;
    }

    const result = await getNotifications(adminId, UserType.ADMIN, page, limit);
    sendSuccess(res, result, 'Admin notifications fetched successfully');
  } catch (error) {
    console.error('Error fetching admin notifications:', error);
    sendError(res, 'Failed to fetch admin notifications', 500);
  }
};

export const getAdminUnreadCountController = async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).admin?.id;
    if (!adminId) {
      sendError(res, 'Admin not authenticated', 401);
      return;
    }

    const count = await getUnreadCount(adminId, UserType.ADMIN);
    sendSuccess(res, { count }, 'Admin unread count fetched successfully');
  } catch (error) {
    console.error('Error fetching admin unread count:', error);
    sendError(res, 'Failed to fetch admin unread count', 500);
  }
};

export const markAdminAsReadController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    await markAsRead(id);
    sendSuccess(res, null, 'Admin notification marked as read');
  } catch (error) {
    console.error('Error marking admin notification as read:', error);
    sendError(res, 'Failed to mark admin notification as read', 500);
  }
};

export const markAllAdminAsReadController = async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).admin?.id;
    if (!adminId) {
      sendError(res, 'Admin not authenticated', 401);
      return;
    }

    await markAllAsRead(adminId, UserType.ADMIN);
    sendSuccess(res, null, 'All admin notifications marked as read');
  } catch (error) {
    console.error('Error marking all admin notifications as read:', error);
    sendError(res, 'Failed to mark all admin notifications as read', 500);
  }
};

export const deleteAllAdminNotificationsController = async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).admin?.id;
    if (!adminId) {
      sendError(res, 'Admin not authenticated', 401);
      return;
    }

    await deleteAllNotifications(adminId, UserType.ADMIN);
    sendSuccess(res, null, 'All admin notifications deleted successfully');
  } catch (error) {
    console.error('Error deleting all admin notifications:', error);
    sendError(res, 'Failed to delete all admin notifications', 500);
  }
};

// Microservice endpoints
export const createSingleNotificationController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, userType, type, title, message, data } = req.body;

    if (!userId || !userType || !type || !title || !message) {
      sendError(res, 'Missing required fields', 400);
      return;
    }

    const notification = await createNotification({
      userId,
      userType,
      type,
      title,
      message,
      data
    });

    sendSuccess(res, notification, 'Notification created successfully');
  } catch (error) {
    console.error('Error creating notification from microservice:', error);
    sendError(res, 'Failed to create notification', 500);
  }
};

export const createAdminNotificationController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type, title, message, data } = req.body;

    if (!type || !title || !message) {
      sendError(res, 'Missing required fields', 400);
      return;
    }

    const notifications = await createAdminNotification({
      type,
      title,
      message,
      data
    });

    sendSuccess(res, notifications, 'Admin notifications created successfully');
  } catch (error) {
    console.error('Error creating admin notification from microservice:', error);
    sendError(res, 'Failed to create admin notification', 500);
  }
};
