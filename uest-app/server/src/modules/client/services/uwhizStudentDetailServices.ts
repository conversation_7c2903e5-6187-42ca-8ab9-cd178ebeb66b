import prisma from '@/config/prismaClient';

export const getStudentProfileByStudentId = async (studentId: string): Promise<any> => {
  const profile = await prisma.studentProfile.findUnique({
    where: { studentId },
    select: {
      classroom: true,
      medium: true,
      status : true,
    },
  });

  if (!profile) {
    throw new Error('Student profile not found');
  }

  return {
    classroom: profile.classroom,
    medium: profile.medium,
    status: profile.status,
  };
};