import { Request, Response } from "express";
import { getUwhizTerminatedStudents } from "../services/getUwhizTeminatedStudentServices";

export const getUwhizTerminatedStudentsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { applicantIds } = req.body;

    if (!applicantIds || !Array.isArray(applicantIds)) {
      return res.status(400).json({ message: "applicantIds must be a non-empty array" });
    }

    const data = await getUwhizTerminatedStudents(applicantIds);
    res.json(data);
  } catch (error: any) {
    console.error("Error in getUwhizTerminatedStudentsController:", error);
    res.status(500).json({
      message: "Error fetching user details",
    });
  }
};