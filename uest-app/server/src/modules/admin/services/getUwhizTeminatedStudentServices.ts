import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export const getUwhizTerminatedStudents = async (applicantIds: any) => {
  const users = await prisma.student.findMany({
    where: {
      id: { in: applicantIds },
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      contact: true,
      createdAt: true,
      profile: {
        select: {
          photo: true,
        },
      },
    },
  });

  return users;
};
