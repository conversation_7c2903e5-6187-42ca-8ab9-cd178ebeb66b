import { axiosInstance } from "@/lib/axios";

export const getMockExamLeaderboard = async (
  timeframe: string,
  page: number = 1,
  limit: number = 10
): Promise<any> => {
  try {
    const response = await axiosInstance.get(
      `/mock-exam-leaderboard/leaderboard/${timeframe}?page=${page}&limit=${limit}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get mock exam leaderboard data: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};

export const storeReaction = async (
  studentId: string,
  reactionType: string,
  reactorId: string | null
): Promise<any> => {
  try {
    const response = await axiosInstance.post(
      `/reactions`,
      {
        studentId,
        reactionType,
        reactorId
      },
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to send reaction: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};

export const getStudentDetails = async (studentIds: string[]): Promise<any> => {
  try {
    const response = await axiosInstance.post(
      `/uwhiz-terminated-students`,
      { applicantIds: studentIds },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const studentDetails = response.data;

    const enrichedDetails = studentIds.map((id) => {
      const student = studentDetails.find((s: any) => s.id === id);
      return student || {
        id,
        firstName: "Unknown",
        lastName: "Student",
        email: null,
        profile: { photo: null },
      };
    });

    return { success: true, data: enrichedDetails };
  } catch (error: any) {
    console.error("Failed to fetch student details:", error);
    const fallbackDetails = studentIds.map((id) => ({
      id,
      firstName: "Unknown",
      lastName: "Student",
      email: null,
      profile: { photo: null },
    }));
    return {
      success: false,
      data: fallbackDetails,
      error: `Failed to get student details: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};